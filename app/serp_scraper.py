# app/serp_scraper.py

import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import Browser<PERSON>onfig, CrawlerRunConfig, CacheMode, ProxyConfig


async def scrape_site(url: str, use_proxy: bool = False) -> str:
    """
    Uses Crawl4AI to scrape a website and return the first 300 chars of content.
    """
    if use_proxy:
        print("Using proxy)...")
        # Create a proper ProxyConfig object
        proxy_config = ProxyConfig(
            server="http://core-residential.evomi-proxy.com:1001",
            username="amitabhme0",
            password="3UGpumdb3dMG2ebJmIcz"
        )

        browser_config = BrowserConfig(
            proxy="http://core-residential.evomi.com:1000:amitabhme0:3UGpumdb3dMG2ebJmIcz")
    else:
        print("Using direct connection (no proxy)...")
        # Use without proxy for testing
        browser_config = BrowserConfig(
            verbose=True
        )
    # run_config = CrawlerRunConfig()
    run_config = CrawlerRunConfig(
        word_count_threshold=10,        # Minimum words per content block
        excluded_tags=['form', 'header'],
        exclude_external_links=True,    # Remove external links
        remove_overlay_elements=True,   # Remove popups/modals
        process_iframes=True,           # Process iframe content
        # Cache control
        cache_mode=CacheMode.DISABLED  # Use cache if available
    )
    async with AsyncWebCrawler(config=browser_config) as crawler:
        print("URL structure: ", url)
        result = await crawler.arun(
            url=url,
            config=run_config

        )
        return result


def run_scrape_demo():
    """
    Wrapper to run the coroutine using asyncio.
    """
    # Test with a more reliable URL first, without proxy
    # sample_url = "https://httpbin.org/html"
    sample_url = "https://example.com"
    print(f"Testing scraping: {sample_url}")

    result = asyncio.run(scrape_site(sample_url, use_proxy=True))
    if not result.success:
        print(f"Crawl failed: {result.error_message}")
        print(f"Status code: {result.status_code}")
    if result.success:
        print("Result::")
        # Different content formats
        # print(result.html)         # Raw HTML
        # print(result.cleaned_html)  # Cleaned HTML
        print(result.markdown.raw_markdown)  # Raw markdown from cleaned html
        # print(result.markdown.fit_markdown)  # Most relevant content in markdown

        # # Check success status
        # print(result.success)      # True if crawl succeeded
        # print(result.status_code)  # HTTP status code (e.g., 200, 404)

        # # Access extracted media and links
        # # Dictionary of found media (images, videos, audio)
       # Process images
        for image in result.media["images"]:
            print(f"Found image: {image['src']}")

        # Process links
        for link in result.links["internal"]:
            # Dictionary of internal and external links
            print(f"Internal link: {link['href']}")
