# app/serp_scraper.py

import asyncio
from crawl4ai import Async<PERSON>ebCrawler
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import Browser<PERSON>onfig, CrawlerRunConfig, CacheMode


async def scrape_site(url: str) -> str:
    """
    Uses Crawl4AI to scrape a website and return the first 300 chars of content.
    """
    proxy_config_http = {
        "server": "core-residential.evomi.com:1000",
        "username": "amitabhme0",
        "password": "3UGpumdb3dMG2ebJmIcz"
    }
    proxy_config_https = {
        "server": "core-residential.evomi-proxy.com:1001",
        "username": "amitabhme0",
        "password": "3UGpumdb3dMG2ebJmIcz"
    }
    # browser_config = BrowserConfig()
    browser_config = BrowserConfig(
        verbose=True,
        proxy_config=proxy_config_https
    )
    # run_config = CrawlerRunConfig()
    run_config = CrawlerRunConfig(
        word_count_threshold=10,        # Minimum words per content block
        excluded_tags=['form', 'header'],
        exclude_external_links=True,    # Remove external links
        remove_overlay_elements=True,   # Remove popups/modals
        process_iframes=True,           # Process iframe content
        # Cache control
        cache_mode=CacheMode.ENABLED  # Use cache if available
    )
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_config
        )
        return result


def run_scrape_demo():
    """
    Wrapper to run the coroutine using asyncio.
    """
    sample_url = "https://www.example.com"
    result = asyncio.run(scrape_site(sample_url))
    if not result.success:
        print(f"Crawl failed: {result.error_message}")
        print(f"Status code: {result.status_code}")
    if result.success:
        print("Result::")
        # Different content formats
        # print(result.html)         # Raw HTML
        print(result.cleaned_html)  # Cleaned HTML
        # print(result.markdown.raw_markdown)  # Raw markdown from cleaned html
        # print(result.markdown.fit_markdown)  # Most relevant content in markdown

        # # Check success status
        # print(result.success)      # True if crawl succeeded
        # print(result.status_code)  # HTTP status code (e.g., 200, 404)

        # # Access extracted media and links
        # # Dictionary of found media (images, videos, audio)
       # Process images
        for image in result.media["images"]:
            print(f"Found image: {image['src']}")

        # Process links
        for link in result.links["internal"]:
            # Dictionary of internal and external links
            print(f"Internal link: {link['href']}")
