# app/serp_scraper.py

import asyncio
from crawl4ai import AsyncWebCrawler


async def scrape_site(url: str) -> str:
    """
    Uses Crawl4AI to scrape a website and return the first 300 chars of content.
    """
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(url=url)
        return result.markdown[:300]


def run_scrape_demo():
    """
    Wrapper to run the coroutine using asyncio.
    """
    sample_url = "https://www.example.com"
    markdown_preview = asyncio.run(scrape_site(sample_url))
    print("\n--- Extracted Content (First 300 chars) ---\n")
    print(markdown_preview)
