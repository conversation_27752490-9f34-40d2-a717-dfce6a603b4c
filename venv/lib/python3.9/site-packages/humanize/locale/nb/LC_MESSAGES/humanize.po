# Norwegian Bokmal translations for PACKAGE package.
# Copyright (C) 2023 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-21 12:09+0100\n"
"PO-Revision-Date: 2023-11-21 12:57+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Norwegian Bokmal <<EMAIL>>\n"
"Language: nb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#: src/humanize/number.py:83
msgctxt "0 (male)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:84
msgctxt "1 (male)"
msgid "st"
msgstr "ste"

#: src/humanize/number.py:85
msgctxt "2 (male)"
msgid "nd"
msgstr "dre"

#: src/humanize/number.py:86
msgctxt "3 (male)"
msgid "rd"
msgstr "dje"

#: src/humanize/number.py:87
msgctxt "4 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:88
msgctxt "5 (male)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:89
msgctxt "6 (male)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:90
msgctxt "7 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:91
msgctxt "8 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:92
msgctxt "9 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:96
msgctxt "0 (female)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:97
msgctxt "1 (female)"
msgid "st"
msgstr "ste"

#: src/humanize/number.py:98
msgctxt "2 (female)"
msgid "nd"
msgstr "dre"

#: src/humanize/number.py:99
msgctxt "3 (female)"
msgid "rd"
msgstr "dje"

#: src/humanize/number.py:100
msgctxt "4 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:101
msgctxt "5 (female)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:102
msgctxt "6 (female)"
msgid "th"
msgstr "te"

#: src/humanize/number.py:103
msgctxt "7 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:104
msgctxt "8 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:105
msgctxt "9 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "tusen"
msgstr[1] "tusen"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "million"
msgstr[1] "millioner"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "milliard"
msgstr[1] "milliarder"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "billion"
msgstr[1] "billioner"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "billiard"
msgstr[1] "billiarder"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "trillion"
msgstr[1] "trillioner"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "trilliard"
msgstr[1] "trilliarder"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kvadrillion"
msgstr[1] "kvadrillioner"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kvadrilliard"
msgstr[1] "kvadrilliarder"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "kvintillion"
msgstr[1] "kvintillioner"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "kvintilliard"
msgstr[1] "kvintilliarder"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:304
msgid "zero"
msgstr "null"

#: src/humanize/number.py:305
msgid "one"
msgstr "en"

#: src/humanize/number.py:306
msgid "two"
msgstr "to"

#: src/humanize/number.py:307
msgid "three"
msgstr "tre"

#: src/humanize/number.py:308
msgid "four"
msgstr "fire"

#: src/humanize/number.py:309
msgid "five"
msgstr "fem"

#: src/humanize/number.py:310
msgid "six"
msgstr "seks"

#: src/humanize/number.py:311
msgid "seven"
msgstr "syv"

#: src/humanize/number.py:312
msgid "eight"
msgstr "åtte"

#: src/humanize/number.py:313
msgid "nine"
msgstr "ni"

#: src/humanize/time.py:151
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosekund"
msgstr[1] "%d mikrosekunder"

#: src/humanize/time.py:160
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d millisekund"
msgstr[1] "%d millisekunder"

#: src/humanize/time.py:163 src/humanize/time.py:262
msgid "a moment"
msgstr "et øyeblikk"

#: src/humanize/time.py:166
msgid "a second"
msgstr "et sekund"

#: src/humanize/time.py:169
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekund"
msgstr[1] "%d sekunder"

#: src/humanize/time.py:172
msgid "a minute"
msgstr "et minutt"

#: src/humanize/time.py:176
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minutt"
msgstr[1] "%d minutter"

#: src/humanize/time.py:179
msgid "an hour"
msgstr "en time"

#: src/humanize/time.py:183
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d time"
msgstr[1] "%d timer"

#: src/humanize/time.py:187
msgid "a day"
msgstr "en dag"

#: src/humanize/time.py:190 src/humanize/time.py:193
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dag"
msgstr[1] "%d dager"

#: src/humanize/time.py:196
msgid "a month"
msgstr "en måned"

#: src/humanize/time.py:198
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d måned"
msgstr[1] "%d måneder"

#: src/humanize/time.py:202
msgid "a year"
msgstr "et år"

#: src/humanize/time.py:205 src/humanize/time.py:216
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 år, %d dag"
msgstr[1] "1 år, %d dager"

#: src/humanize/time.py:209
msgid "1 year, 1 month"
msgstr "1 år, 1 måned"

#: src/humanize/time.py:212
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 år, %d måned"
msgstr[1] "1 år, %d måneder"

#: src/humanize/time.py:218
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d år"
msgstr[1] "%d år"

#: src/humanize/time.py:259
#, python-format
msgid "%s from now"
msgstr "%s fra nå"

#: src/humanize/time.py:259
#, python-format
msgid "%s ago"
msgstr "%s siden"

#: src/humanize/time.py:263
msgid "now"
msgstr "nå"

#: src/humanize/time.py:296
msgid "today"
msgstr "i dag"

#: src/humanize/time.py:299
msgid "tomorrow"
msgstr "i morgen"

#: src/humanize/time.py:302
msgid "yesterday"
msgstr "i går"

#: src/humanize/time.py:613
#, python-format
msgid "%s and %s"
msgstr "%s og %s"
