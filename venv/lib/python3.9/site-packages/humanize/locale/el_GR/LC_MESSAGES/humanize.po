# Greek translations for PACKAGE package.
# Copyright (C) 2022 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON> <<EMAIL>>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2022-08-05 01:09+0300\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Greek <<EMAIL>>\n"
"Language: el\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: <PERSON><PERSON>\n"
"X-Generator: Mousepad 0.5.9\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr ""

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr ""

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr ""

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr ""

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr ""

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr ""

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr ""

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr ""

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "χιλιάδα"
msgstr[1] "χιλιάδες"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "εκατομμύριο"
msgstr[1] "εκατομμύρια"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "δισεκατομμύριο"
msgstr[1] "δισεκατομμύρια"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "τρισεκατομμύριο"
msgstr[1] "τρισεκατομμύρια"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "τετράκις εκατομμύριο"
msgstr[1] "τετράκις εκατομμύρια"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "πεντάκις εκατομμύριο"
msgstr[1] "πεντάκις εκατομμύρια"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "εξάκις εκατομμύριο"
msgstr[1] "εξάκις εκατομμύρια"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "επτάκις εκατομμύριο"
msgstr[1] "επτάκις εκατομμύρια"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "οκτάκις εκατομμύριο"
msgstr[1] "οκτάκις εκατομμύρια"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "εννεάκις εκατομμύριο"
msgstr[1] "εννεάκις εκατομμύρια"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "δεκάκις εκατομμύριο"
msgstr[1] "δεκάκις εκατομμύρια"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "δέκα τριακονταδυάκις εκατομμύριο"
msgstr[1] "δέκα τριακονταδυάκις εκατομμύρια"

#: src/humanize/number.py:301
msgid "zero"
msgstr "μηδέν"

#: src/humanize/number.py:302
msgid "one"
msgstr "ένα"

#: src/humanize/number.py:303
msgid "two"
msgstr "δύο"

#: src/humanize/number.py:304
msgid "three"
msgstr "τρία"

#: src/humanize/number.py:305
msgid "four"
msgstr "τέσσερα"

#: src/humanize/number.py:306
msgid "five"
msgstr "πέντε"

#: src/humanize/number.py:307
msgid "six"
msgstr "έξι"

#: src/humanize/number.py:308
msgid "seven"
msgstr "επτά"

#: src/humanize/number.py:309
msgid "eight"
msgstr "οκτώ"

#: src/humanize/number.py:310
msgid "nine"
msgstr "εννέα"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d εκατομμυριοστό του δευτερολέπτου"
msgstr[1] "%d εκατομμυριοστά του δευτερολέπτου"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d χιλιοστό του δευτερολέπτου"
msgstr[1] "%d χιλιοστά του δευτερολέπτου"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "μια στιγμή"

#: src/humanize/time.py:167
msgid "a second"
msgstr "ένα δευτερόλεπτο"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d δευτερόλεπτο"
msgstr[1] "%d δευτερόλεπτα"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "ένα λεπτό"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d λεπτό"
msgstr[1] "%d λεπτά"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "μία ώρα"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ώρα"
msgstr[1] "%d ώρες"

#: src/humanize/time.py:188
msgid "a day"
msgstr "μία ημέρα"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d ημέρα"
msgstr[1] "%d ημέρες"

#: src/humanize/time.py:197
msgid "a month"
msgstr "ένα μήνα"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d μήνα"
msgstr[1] "%d μήνες"

#: src/humanize/time.py:203
msgid "a year"
msgstr "ένα έτος"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "ένα έτος και %d ημέρα"
msgstr[1] "ένα έτος και %d ημέρες"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "ένα έτος και ένα μήνα"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "ένα έτος και %d μήνα"
msgstr[1] "ένα έτος και %d μήνες"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d έτος"
msgstr[1] "%d έτη"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "σε %s από τώρα"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "πριν από %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "τώρα"

#: src/humanize/time.py:284
msgid "today"
msgstr "σήμερα"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "αύριο"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "χθες"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s και %s"

#~ msgctxt "0 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "1 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "2 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "3 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "4 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "5 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "6 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "7 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "8 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "9 (male)"
#~ msgid "ος"
#~ msgstr "."

#~ msgctxt "0 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "1 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "2 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "3 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "4 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "5 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "6 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "7 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "8 (female)"
#~ msgid "η"
#~ msgstr "."

#~ msgctxt "9 (female)"
#~ msgid "η"
#~ msgstr "."
