# Esperanto translations for PACKAGE package.
# Copyright (C) 2023 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-02 10:46-0700\n"
"PO-Revision-Date: 2023-05-02 10:50-0700\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Esperanto <<EMAIL>>\n"
"Language: eo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "-a"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "-a"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "-a"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "-a"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "-a"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "-a"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "-a"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "mil"
msgstr[1] "mil"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "miliono"
msgstr[1] "miliono"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miliardo"
msgstr[1] "miliardo"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "duiliono"
msgstr[1] "duiliono"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "duiliardo"
msgstr[1] "duiliardo"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "triiliono"
msgstr[1] "triiliono"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "triiliardo"
msgstr[1] "triiliardo"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kvariliono"
msgstr[1] "kvariliono"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kvariliardo"
msgstr[1] "kvariliardo"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "sesiliono"
msgstr[1] "sesiliono"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "sesiliardo"
msgstr[1] "sesiliardo"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "guglo"
msgstr[1] "guglo"

#: src/humanize/number.py:302
msgid "zero"
msgstr "nul"

#: src/humanize/number.py:303
msgid "one"
msgstr "unu"

#: src/humanize/number.py:304
msgid "two"
msgstr "du"

#: src/humanize/number.py:305
msgid "three"
msgstr "tri"

#: src/humanize/number.py:306
msgid "four"
msgstr "kvar"

#: src/humanize/number.py:307
msgid "five"
msgstr "kvin"

#: src/humanize/number.py:308
msgid "six"
msgstr "ses"

#: src/humanize/number.py:309
msgid "seven"
msgstr "sep"

#: src/humanize/number.py:310
msgid "eight"
msgstr "ok"

#: src/humanize/number.py:311
msgid "nine"
msgstr "naŭ"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milisekundo"
msgstr[1] "%d milisekundoj"

#: src/humanize/time.py:164 src/humanize/time.py:263
msgid "a moment"
msgstr "momento"

#: src/humanize/time.py:167
msgid "a second"
msgstr "sekundo"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekundo"
msgstr[1] "%d sekundoj"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "minuto"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuto"
msgstr[1] "%d minutoj"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "horo"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d horo"
msgstr[1] "%d horoj"

#: src/humanize/time.py:188
msgid "a day"
msgstr "tago"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d tago"
msgstr[1] "%d tagoj"

#: src/humanize/time.py:197
msgid "a month"
msgstr "monato"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d monato"
msgstr[1] "%d monotoj"

#: src/humanize/time.py:203
msgid "a year"
msgstr "jaro"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 jaro, %d tago"
msgstr[1] "1 jaro, %d tagoj"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 jaro, 1 monato"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 jaro, %d monato"
msgstr[1] "1 jaro, %d monatoj"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d jaro"
msgstr[1] "%d jaroj"

#: src/humanize/time.py:260
#, python-format
msgid "%s from now"
msgstr "post %s"

#: src/humanize/time.py:260
#, python-format
msgid "%s ago"
msgstr "antaŭ %s"

#: src/humanize/time.py:264
msgid "now"
msgstr "nun"

#: src/humanize/time.py:288
msgid "today"
msgstr "hodiaŭ"

#: src/humanize/time.py:291
msgid "tomorrow"
msgstr "morgaŭ"

#: src/humanize/time.py:294
msgid "yesterday"
msgstr "hieraŭ"

#: src/humanize/time.py:604
#, python-format
msgid "%s and %s"
msgstr "%s kaj %s"
