# Basque translations for PACKAGE package.
# Copyright (C) 2023 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2023-01-02 20:35+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Basque <<EMAIL>>\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=ASCII\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "mila"
msgstr[1] "mila"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milioi"
msgstr[1] "milioi"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miliar"
msgstr[1] "miliar"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "trilioi"
msgstr[1] "trilioi"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "kuatrilioi"
msgstr[1] "kuatrilioi"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "zero"

#: src/humanize/number.py:302
msgid "one"
msgstr "bat"

#: src/humanize/number.py:303
msgid "two"
msgstr "bi"

#: src/humanize/number.py:304
msgid "three"
msgstr "hiru"

#: src/humanize/number.py:305
msgid "four"
msgstr "lau"

#: src/humanize/number.py:306
msgid "five"
msgstr "bost"

#: src/humanize/number.py:307
msgid "six"
msgstr "sei"

#: src/humanize/number.py:308
msgid "seven"
msgstr "zazpi"

#: src/humanize/number.py:309
msgid "eight"
msgstr "zortzi"

#: src/humanize/number.py:310
msgid "nine"
msgstr "bederatzi"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "mikrosegundo %d"
msgstr[1] "%d mikrosegundo"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "milisegundo %d"
msgstr[1] "%d milisegundo"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "une bat"

#: src/humanize/time.py:167
msgid "a second"
msgstr "segundo bat"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "segundo %d"
msgstr[1] "%d segundo"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "minutu bat"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "minutu %d"
msgstr[1] "%d minutu"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "ordu bat"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "ordu %d"
msgstr[1] "%d ordu"

#: src/humanize/time.py:188
msgid "a day"
msgstr "egun bat"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "egun %d"
msgstr[1] "%d egun"

#: src/humanize/time.py:197
msgid "a month"
msgstr "hilabete bat"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "hilabete %d"
msgstr[1] "%d hilabete"

#: src/humanize/time.py:203
msgid "a year"
msgstr "urte bat"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "urte 1, egun %d"
msgstr[1] "urte 1, %d egun"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "urte 1, hilabete 1"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "urte 1, hilabete %d"
msgstr[1] "urte 1, %d hilabete"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "urte %d"
msgstr[1] "%d urte"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "hemendik %s-(e)ra"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "orain dela %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "orain"

#: src/humanize/time.py:284
msgid "today"
msgstr "gaur"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "bihar"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "atzo"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s eta %s"
