# Arabic (عربي) translations for humanize package.
# Copyright (C) 2022.
# This file is distributed under the same license as the humanize package.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# YazeedT, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2022-04-13 17:28+0300\n"
"Last-Translator: AYME<PERSON> <<EMAIL>>\n"
"Language-Team: Arabic <<EMAIL>>\n"
"Language: Arabic (عربي)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "صفر"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "اول"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "ثاني"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "ثالث"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "رابع"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "خامس"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "سادس"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "سابع"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "ثامن"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "تاسع"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "صفر"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "اولى"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "ثانية"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "ثالثة"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "رابعة"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "خامسة"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "سادسة"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "سابعة"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "ثامنة"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "تاسعة"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "الف"
msgstr[1] "الاف"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "مليون"
msgstr[1] "ملايين"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "مليار"
msgstr[1] "مليارات"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "تريليون"
msgstr[1] "تريليونات"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "كوادريليون"
msgstr[1] "كوادريليونات"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "كوينتيليون"
msgstr[1] "كوينتيليونات"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "سكستليون"
msgstr[1] "سكستليونات"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "سبتيليون"
msgstr[1] "سبتيليونات"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "اوكتيليون"
msgstr[1] "اوكتيليونات"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "نونليون"
msgstr[1] "نونليونات"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "ديليون"
msgstr[1] "ديليونات"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "جوجل"
msgstr[1] "جوجلات"

#: src/humanize/number.py:301
msgid "zero"
msgstr "صفر"

#: src/humanize/number.py:302
msgid "one"
msgstr "واحد"

#: src/humanize/number.py:303
msgid "two"
msgstr "اثنين"

#: src/humanize/number.py:304
msgid "three"
msgstr "ثلاثة"

#: src/humanize/number.py:305
msgid "four"
msgstr "اربعة"

#: src/humanize/number.py:306
msgid "five"
msgstr "خمسة"

#: src/humanize/number.py:307
msgid "six"
msgstr "ستة"

#: src/humanize/number.py:308
msgid "seven"
msgstr "سبعة"

#: src/humanize/number.py:309
msgid "eight"
msgstr "ثمانية"

#: src/humanize/number.py:310
msgid "nine"
msgstr "تسعة"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d ميكرو من الثانية"
msgstr[1] "%d ميكرو من الثانية"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d جزء من الثانية"
msgstr[1] "%d اجزاء من الثانية"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "لحظة"

#: src/humanize/time.py:167
msgid "a second"
msgstr "ثانية"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d ثانية"
msgstr[1] "%d ثواني"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "دقيقة"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d دقيقة"
msgstr[1] "%d دقائق"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "ساعة"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ساعة"
msgstr[1] "%d ساعات"

#: src/humanize/time.py:188
msgid "a day"
msgstr "يوم"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d يوم"
msgstr[1] "%d أيام"

#: src/humanize/time.py:197
msgid "a month"
msgstr "شهر"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d شهر"
msgstr[1] "%d أشهر"

#: src/humanize/time.py:203
msgid "a year"
msgstr "سنة"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 سنة ، %d يوم"
msgstr[1] "1 سنة ، %d ايام"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "سنة وشهر"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 سنة ، %d شهر"
msgstr[1] "1 سنة ، %d اشهر"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d سنة"
msgstr[1] "%d سنين"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s من الان"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "قبل %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "الان"

#: src/humanize/time.py:284
msgid "today"
msgstr "اليوم"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "غداً"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "أمس"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s و %s"
