[{"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "CrawlerRunConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "CacheMode", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "run_scrape_demo", "kind": 2, "importPath": "app.serp_scraper", "description": "app.serp_scraper", "peekOfCode": "def run_scrape_demo():\n    \"\"\"\n    Wrapper to run the coroutine using asyncio.\n    \"\"\"\n    sample_url = \"https://www.example.com\"\n    result = asyncio.run(scrape_site(sample_url))\n    if not result.success:\n        print(f\"Crawl failed: {result.error_message}\")\n        print(f\"Status code: {result.status_code}\")\n    if result.success:", "detail": "app.serp_scraper", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.run_scraper", "description": "scripts.run_scraper", "peekOfCode": "def main():\n    # Import here after sys.path is modified\n    from app.serp_scraper import run_scrape_demo\n    run_scrape_demo()\nif __name__ == \"__main__\":\n    main()", "detail": "scripts.run_scraper", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "scripts.run_scraper", "description": "scripts.run_scraper", "peekOfCode": "project_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\ndef main():\n    # Import here after sys.path is modified\n    from app.serp_scraper import run_scrape_demo\n    run_scrape_demo()\nif __name__ == \"__main__\":\n    main()", "detail": "scripts.run_scraper", "documentation": {}}]