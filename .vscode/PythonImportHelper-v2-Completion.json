[{"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "CrawlerRunConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "CacheMode", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "ProxyConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "httpx", "kind": 6, "isExtraImport": true, "importPath": "httpx", "description": "httpx", "detail": "httpx", "documentation": {}}, {"label": "base64", "kind": 6, "isExtraImport": true, "importPath": "base64", "description": "base64", "detail": "base64", "documentation": {}}, {"label": "run_scrape_demo", "kind": 2, "importPath": "app.serp_scraper", "description": "app.serp_scraper", "peekOfCode": "def run_scrape_demo():\n    \"\"\"\n    Wrapper to run the coroutine using asyncio.\n    \"\"\"\n    # Test with a more reliable URL first, without proxy\n    # sample_url = \"https://httpbin.org/html\"\n    sample_url = \"https://example.com\"\n    print(f\"Testing scraping: {sample_url}\")\n    result = asyncio.run(scrape_site(sample_url, use_proxy=True))\n    if not result.success:", "detail": "app.serp_scraper", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.run_scraper", "description": "scripts.run_scraper", "peekOfCode": "def main():\n    # Import here after sys.path is modified\n    from app.serp_scraper import run_scrape_demo\n    run_scrape_demo()\nif __name__ == \"__main__\":\n    main()", "detail": "scripts.run_scraper", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "scripts.run_scraper", "description": "scripts.run_scraper", "peekOfCode": "project_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\ndef main():\n    # Import here after sys.path is modified\n    from app.serp_scraper import run_scrape_demo\n    run_scrape_demo()\nif __name__ == \"__main__\":\n    main()", "detail": "scripts.run_scraper", "documentation": {}}, {"label": "username", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "username = \"amitabhme0_country-AU_region-tasmania_city-melbourne\"\npassword = \"3UGpumdb3dMG2ebJmIcz\"\nproxy_host = \"core-residential.evomi-proxy.com\"\nproxy_port = 1001\n# Base64-encoded credentials\nbasic_auth = base64.b64encode(f\"{username}:{password}\".encode()).decode()\nproxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {", "detail": "test_proxy_request", "documentation": {}}, {"label": "password", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "password = \"3UGpumdb3dMG2ebJmIcz\"\nproxy_host = \"core-residential.evomi-proxy.com\"\nproxy_port = 1001\n# Base64-encoded credentials\nbasic_auth = base64.b64encode(f\"{username}:{password}\".encode()).decode()\nproxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",", "detail": "test_proxy_request", "documentation": {}}, {"label": "proxy_host", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "proxy_host = \"core-residential.evomi-proxy.com\"\nproxy_port = 1001\n# Base64-encoded credentials\nbasic_auth = base64.b64encode(f\"{username}:{password}\".encode()).decode()\nproxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",\n}", "detail": "test_proxy_request", "documentation": {}}, {"label": "proxy_port", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "proxy_port = 1001\n# Base64-encoded credentials\nbasic_auth = base64.b64encode(f\"{username}:{password}\".encode()).decode()\nproxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",\n}\nurl = \"https://example.com\"", "detail": "test_proxy_request", "documentation": {}}, {"label": "basic_auth", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "basic_auth = base64.b64encode(f\"{username}:{password}\".encode()).decode()\nproxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",\n}\nurl = \"https://example.com\"\nwith httpx.Client(proxies=proxies, headers=headers) as client:\n    try:", "detail": "test_proxy_request", "documentation": {}}, {"label": "proxies", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "proxies = {\n    \"https://\": f\"http://{proxy_host}:{proxy_port}\",\n}\nheaders = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",\n}\nurl = \"https://example.com\"\nwith httpx.Client(proxies=proxies, headers=headers) as client:\n    try:\n        response = client.get(url, timeout=10)", "detail": "test_proxy_request", "documentation": {}}, {"label": "headers", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "headers = {\n    \"Proxy-Authorization\": f\"Basic {basic_auth}\",\n}\nurl = \"https://example.com\"\nwith httpx.Client(proxies=proxies, headers=headers) as client:\n    try:\n        response = client.get(url, timeout=10)\n        print(\"Status:\", response.status_code)\n        print(\"Body:\\n\", response.text[:300])\n    except Exception as e:", "detail": "test_proxy_request", "documentation": {}}, {"label": "url", "kind": 5, "importPath": "test_proxy_request", "description": "test_proxy_request", "peekOfCode": "url = \"https://example.com\"\nwith httpx.Client(proxies=proxies, headers=headers) as client:\n    try:\n        response = client.get(url, timeout=10)\n        print(\"Status:\", response.status_code)\n        print(\"Body:\\n\", response.text[:300])\n    except Exception as e:\n        print(\"Request failed:\", str(e))", "detail": "test_proxy_request", "documentation": {}}]