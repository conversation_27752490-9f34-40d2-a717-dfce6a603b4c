[{"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "run_scrape_demo", "importPath": "app.serp_scraper", "description": "app.serp_scraper", "isExtraImport": true, "detail": "app.serp_scraper", "documentation": {}}, {"label": "run_scrape_demo", "kind": 2, "importPath": "app.serp_scraper", "description": "app.serp_scraper", "peekOfCode": "def run_scrape_demo():\n    \"\"\"\n    Wrapper to run the coroutine using asyncio.\n    \"\"\"\n    sample_url = \"https://www.example.com\"\n    markdown_preview = asyncio.run(scrape_site(sample_url))\n    print(\"\\n--- Extracted Content (First 300 chars) ---\\n\")\n    print(markdown_preview)", "detail": "app.serp_scraper", "documentation": {}}]