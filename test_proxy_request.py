import requests

# Corrected username with geo-targeting
username = "amitabhme0_country-AU_region-tasmania_city-melbourne"
password = "3UGpumdb3dMG2ebJmIcz"
proxy_host = "core-residential.evomi-proxy.com"
proxy_port = 1001

proxy_url = f"https://{username}:{password}@{proxy_host}:{proxy_port}"
print("Using proxy:", proxy_url)

proxies = {
    "http": proxy_url,
    "https": proxy_url
}

url = "https://example.com"

try:
    response = requests.get(url, proxies=proxies, timeout=10)
    print("Status Code:", response.status_code)
    print("Content Snippet:\n", response.text[:300])
except Exception as e:
    print("Request failed:", str(e))
